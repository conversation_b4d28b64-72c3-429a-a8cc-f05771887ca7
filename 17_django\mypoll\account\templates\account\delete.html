<!-- account/templates/account/delete.html -->
{% extends 'layouts/main_layout.html' %}
{% load django_bootstrap5 %}

{% block title %}회원탈퇴 - Modern Survey{% endblock %}

{% block contents %}
    <!-- Page header -->
    <header class="py-5">
        <div class="container px-4 px-lg-5 my-5">
            <div class="text-center">
                <h1 class="display-4 fw-bolder text-danger">회원탈퇴</h1>
                <p class="lead fw-normal text-muted mb-0">정말로 탈퇴하시겠습니까? 이 작업은 되돌릴 수 없습니다.</p>
            </div>
        </div>
    </header>

    <!-- Page content -->
    <section class="py-5">
        <div class="container px-4 px-lg-5">
            <div class="row gx-4 gx-lg-5 justify-content-center">
                <div class="col-lg-6 col-xl-5">
                    <!-- Warning card -->
                    <div class="card border-danger shadow">
                        <div class="card-header bg-danger text-white">
                            <h4 class="mb-0">
                                <i class="bi bi-exclamation-triangle-fill me-2"></i>회원탈퇴 확인
                            </h4>
                        </div>
                        
                        <div class="card-body p-4">
                            <!-- Warning message -->
                            <div class="alert alert-warning" role="alert">
                                <h5 class="alert-heading">
                                    <i class="bi bi-exclamation-triangle me-2"></i>주의사항
                                </h5>
                                <p class="mb-0">회원탈퇴 시 다음 정보들이 <strong>영구적으로 삭제</strong>됩니다:</p>
                            </div>

                            <!-- What will be deleted -->
                            <div class="mb-4">
                                <ul class="list-group list-group-flush">
                                    <li class="list-group-item d-flex align-items-center">
                                        <i class="bi bi-person-x text-danger me-3"></i>
                                        <div>
                                            <strong>계정 정보</strong>
                                            <small class="d-block text-muted">사용자명, 이름, 이메일, 생년월일</small>
                                        </div>
                                    </li>
                                    <li class="list-group-item d-flex align-items-center">
                                        <i class="bi bi-hand-index text-danger me-3"></i>
                                        <div>
                                            <strong>투표 기록</strong>
                                            <small class="d-block text-muted">참여한 모든 설문조사 투표 기록</small>
                                        </div>
                                    </li>
                                    <li class="list-group-item d-flex align-items-center">
                                        <i class="bi bi-clock-history text-danger me-3"></i>
                                        <div>
                                            <strong>활동 이력</strong>
                                            <small class="d-block text-muted">로그인 기록, 활동 통계</small>
                                        </div>
                                    </li>
                                </ul>
                            </div>

                            <!-- Current user info -->
                            <div class="bg-light rounded p-3 mb-4">
                                <h6 class="fw-bold mb-2">탈퇴할 계정 정보</h6>
                                <div class="row">
                                    <div class="col-6">
                                        <small class="text-muted">사용자명</small>
                                        <div class="fw-medium">{{ user.username }}</div>
                                    </div>
                                    <div class="col-6">
                                        <small class="text-muted">이름</small>
                                        <div class="fw-medium">{{ user.name|default:"미입력" }}</div>
                                    </div>
                                </div>
                                <div class="row mt-2">
                                    <div class="col-6">
                                        <small class="text-muted">이메일</small>
                                        <div class="fw-medium">{{ user.email|default:"미입력" }}</div>
                                    </div>
                                    <div class="col-6">
                                        <small class="text-muted">가입일</small>
                                        <div class="fw-medium">{{ user.date_joined|date:"Y-m-d" }}</div>
                                    </div>
                                </div>
                            </div>

                            <!-- Confirmation form -->
                            <form method="post" id="deleteForm">
                                {% csrf_token %}
                                
                                <!-- Password confirmation -->
                                <div class="mb-4">
                                    <label for="password" class="form-label fw-bold text-danger">
                                        <i class="bi bi-key me-2"></i>비밀번호 확인
                                    </label>
                                    <input type="password" 
                                           class="form-control" 
                                           id="password" 
                                           name="password" 
                                           placeholder="현재 비밀번호를 입력하세요"
                                           required>
                                    <div class="form-text">보안을 위해 현재 비밀번호를 입력해주세요.</div>
                                </div>

                                <!-- Final confirmation checkbox -->
                                <div class="form-check mb-4">
                                    <input class="form-check-input" type="checkbox" id="confirmDelete" required>
                                    <label class="form-check-label fw-medium" for="confirmDelete">
                                        위 내용을 모두 확인했으며, <strong class="text-danger">회원탈퇴에 동의</strong>합니다.
                                    </label>
                                </div>

                                <!-- Action buttons -->
                                <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                                    <a href="{% url 'account:detail' %}" class="btn btn-outline-secondary me-md-2">
                                        <i class="bi bi-arrow-left me-2"></i>취소
                                    </a>
                                    <button type="submit" class="btn btn-danger" id="deleteButton" disabled>
                                        <i class="bi bi-trash me-2"></i>회원탈퇴
                                    </button>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <script>
        // 체크박스 상태에 따라 버튼 활성화/비활성화
        document.getElementById('confirmDelete').addEventListener('change', function() {
            const deleteButton = document.getElementById('deleteButton');
            deleteButton.disabled = !this.checked;
        });

        // 폼 제출 시 최종 확인
        document.getElementById('deleteForm').addEventListener('submit', function(e) {
            if (!confirm('정말로 회원탈퇴를 진행하시겠습니까?\n\n이 작업은 되돌릴 수 없습니다.')) {
                e.preventDefault();
            }
        });
    </script>

    <style>
        .card {
            animation: fadeInUp 0.6s ease-out;
        }
        
        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }
        
        .list-group-item {
            border-left: none;
            border-right: none;
        }
        
        .list-group-item:first-child {
            border-top: none;
        }
        
        .list-group-item:last-child {
            border-bottom: none;
        }
    </style>
{% endblock contents %}
