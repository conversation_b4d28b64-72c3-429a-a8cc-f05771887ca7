"""
Django settings for config project.

Generated by 'django-admin startproject' using Django 5.2.4.

For more information on this file, see
https://docs.djangoproject.com/en/5.2/topics/settings/

For the full list of settings and their values, see
https://docs.djangoproject.com/en/5.2/ref/settings/
"""

from pathlib import Path

# Build paths inside the project like this: BASE_DIR / 'subdir'.
# project root directory
BASE_DIR = Path(__file__).resolve().parent.parent

# print("----------", __file__) # module의 절대경로를 반환.
# print("----------", BASE_DIR)


# Quick-start development settings - unsuitable for production
# See https://docs.djangoproject.com/en/5.2/howto/deployment/checklist/

# SECURITY WARNING: keep the secret key used in production secret!
SECRET_KEY = 'django-insecure-(x!sf28@1fx6l-o&1ienw6di5_5@^2swhh(&08t=%p%gwgp&mn'

# SECURITY WARNING: don't run with debug turned on in production!
DEBUG = True

ALLOWED_HOSTS = []


# Application definition

INSTALLED_APPS = [
    'django.contrib.admin',
    'django.contrib.auth',
    'django.contrib.contenttypes',
    'django.contrib.sessions',
    'django.contrib.messages',
    'django.contrib.staticfiles',
    'polls',
    'account',
    'django_bootstrap5',
]

MIDDLEWARE = [
    'django.middleware.security.SecurityMiddleware',
    'django.contrib.sessions.middleware.SessionMiddleware',
    'django.middleware.common.CommonMiddleware',
    'django.middleware.csrf.CsrfViewMiddleware',
    'django.contrib.auth.middleware.AuthenticationMiddleware',
    'django.contrib.messages.middleware.MessageMiddleware',
    'django.middleware.clickjacking.XFrameOptionsMiddleware',
]

ROOT_URLCONF = 'config.urls'

TEMPLATES = [
    {
        'BACKEND': 'django.template.backends.django.DjangoTemplates',
        'DIRS': [BASE_DIR / "templates"],
        'APP_DIRS': True,
        'OPTIONS': {
            'context_processors': [
                'django.template.context_processors.request',
                'django.contrib.auth.context_processors.auth',
                'django.contrib.messages.context_processors.messages',
            ],
        },
    },
]

WSGI_APPLICATION = 'config.wsgi.application'


# Database
# https://docs.djangoproject.com/en/5.2/ref/settings/#databases

DATABASES = {
    'default': {
        'ENGINE': 'django.db.backends.sqlite3',
        'NAME': BASE_DIR / 'db.sqlite3',
    }
}


# Password validation
# https://docs.djangoproject.com/en/5.2/ref/settings/#auth-password-validators

AUTH_PASSWORD_VALIDATORS = [
    {
        'NAME': 'django.contrib.auth.password_validation.UserAttributeSimilarityValidator',
    },
    {
        'NAME': 'django.contrib.auth.password_validation.MinimumLengthValidator',
    },
    {
        'NAME': 'django.contrib.auth.password_validation.CommonPasswordValidator',
    },
    {
        'NAME': 'django.contrib.auth.password_validation.NumericPasswordValidator',
    },
]


# Internationalization
# https://docs.djangoproject.com/en/5.2/topics/i18n/

LANGUAGE_CODE = 'ko-kr'

TIME_ZONE = 'Asia/Seoul'

USE_I18N = True

USE_TZ = True


#######################################
# Static files (CSS, JavaScript, Images) 들을 요청할때 사용할 url의 시작
# https://docs.djangoproject.com/en/5.2/howto/static-files/

STATIC_URL = 'static/'
# app/static 이외의 경로에 static 파일들이 있을 경우 그 디렉토리를 설정.
STATICFILES_DIRS = [BASE_DIR / 'staticfiles'] #/static/imgs/survey.jpg

STATIC_ROOT = BASE_DIR / 'statics'
# python manage.py collectstatic  실행하면 모든 static 파일들을 STATIC_ROOT 경로에 모아준다.


# Default primary key field type
# https://docs.djangoproject.com/en/5.2/ref/settings/#default-auto-field

DEFAULT_AUTO_FIELD = 'django.db.models.BigAutoField'

###############################
# 사용자 정의 User 모델을 등록
###############################
AUTH_USER_MODEL = "account.User"  # AbstractUser 클래스를 등록.

################################
# 로그인 관련 설정
################################
# 로그인 하지 않은 사용자가 @login_required 인 View를 호출 했을 때 이동할 url을 지정.
LOGIN_URL = '/account/login'

################################
# MEDIA 설정 (파일 업로드)
################################
# 업로드 파일들을 저장할 root 경로 설정
MEDIA_ROOT = BASE_DIR / 'media'
# Clinet가 업로드된 파일을 요청할 때 사용할 (시작)url설정
MEDIA_URL = '/media/'
