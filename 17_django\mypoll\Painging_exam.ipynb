# django shell 환경설정
import os
import django
os.environ['DJANGO_SETTINGS_MODULE'] = 'config.settings'
os.environ['DJANGO_ALLOW_ASYNC_UNSAFE'] = 'true'

django.setup()

txt = "0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ"
dataset = list(txt)
len(txt), len(dataset)
# dataset

from django.core.paginator import Paginator

# Paging관리 -> Paginator 객체를 생성 -> 전체 데이터를 다 넣어서 생성.
pn = Paginator(dataset, 5) # (전체 데이터들, 페이지당 데이터개수)

print("전체 데이터 개수:", pn.count)
print("총 페이지수:", pn.num_pages)
print("시작페이지번호, 끝 페이지 번호:", pn.page_range)

for page in pn.page_range:
    print(page, end="\t")

# 특정 Page값들을 조회 -> Page객체
page1 = pn.page(1) # 1 page 조회 -> 시작페이지
print(type(page1))
page1

page10 = pn.page(10) # 중간 페이지
page10

page13 = pn.page(13) # 마지막 페이지
page13

# pn.page(100) # 없는 페이지. EmptyPage Exception발생.

# Page객체가 가진 데이터들을 조회 -> Page: Iterable, Subscriptable
# page1[1]
for data in page1:
    print(data, end=", ")
print()
for data in page10:
    print(data, end=", ")
print()
for data in page13:
    print(data, end=", ")

# page의 데이터를 list로 변환
page1.object_list



page1.has_previous(), page1.has_next()

page10.has_previous(), page10.has_next()

page13.has_previous(), page13.has_next()

page1.number, page10.number, page13.number

# 이전 페이지 번호
page10.previous_page_number()
page13.previous_page_number()

if page1.has_previous():
    print(page1.previous_page_number()) # 이전페이지가 없을 경우(1page에서 조회) EmptyPage 예외발생

# 다음 페이지 번호
page1.next_page_number()
page10.next_page_number()

if page13.has_next():
    print(page13.next_page_number())# 다음 페이지가 없을 경우(마지막 page에서 조회) EmptyPage 예외발생

# 한페지당 K개의 데이터를 출력
# ------N 페이지-------
# a, b, c, d, e, ...
paginate_by = 10 # k개
# Paginator객체 생성
pn = Paginator(dataset, paginate_by)
# pn.page_range
# 반복문으로 각 페이지의 데이터를 출력
for page_num in pn.page_range:
    page = pn.page(page_num)
    print(f"--------------page:{page.number}------------------")
    for data in page:
        print(data, end=", ")
    print()



for p in pn.page_range[0:5]:
    print(p, end=', ')

pn = Paginator(dataset, 5)
r = pn.page_range
r

# 하나의 page group에 3페이지씩 묶는 경우.
# index: 0 ~ 2, 3 ~ 5, 6 ~ 8, ...
print(r[0:3]) # 현재 page가 1, 2, 3 일 때, 그 페이지들이 속한 페이지의 시작/끝 index
print(r[3:6]) # 현재 page가 4, 5, 6 일 때, 그 페이지들이 속한 페이지의 시작/끝 index
print(r[6:9]) # 현재 page가 7, 8, 9 일 때, 그 페이지들이 속한 페이지의 시작/끝 index

int(0.99999) # 내림

current_page = 20 # 현재 페이지 번호
page_group_count = 10 # 페이지 그룹당 묶을 페이지 개수 (1 page group: 1, 2, 3, 4, 5 ->의 index를 조회)

start_index = int((current_page - 1)/page_group_count) * page_group_count
end_index = start_index + page_group_count
print(start_index, end_index)

for p in pn.page_range[start_index:end_index]:
    print(p, end=',')

pn.page_range



from polls.models import Question, Choice

for i in range(1, 403): # 402개 추가
    q = Question(question_text=f"질문 - {i}")
    q.save()

# Question 개수 확인
cnt = Question.objects.all().count()
cnt

start_id = Question.objects.all()[0].pk
start_id

# 각 문제당 보기 4개씩 추가
import random
for q in Question.objects.all():
    for i in range(4):  # 4: 보기개수
        choice_text = f"{i}번째 보기입니다."
        votes = random.randint(0, 150)
        c = Choice(choice_text=choice_text, votes=votes, question=q)
        c.save()

Choice.objects.all().count()